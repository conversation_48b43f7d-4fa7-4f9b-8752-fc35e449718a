"""
API routes for the PYQs Admin module.
"""

import os
import math
import logging
import shutil
import asyncio
import tempfile
from datetime import datetime
from pathlib import Path
from typing import Optional, List
from utils.s3_utils import read_file_from_s3

from fastapi import APIRouter, Depends, Request, HTTPException, Form, Query, File, UploadFile, Body
from fastapi.responses import HTMLResponse, RedirectResponse, JSONResponse, FileResponse, StreamingResponse
from fastapi.templating import Jinja2Templates
from auth.dependencies import require_login
from auth.rbac import require_roles
from db_config.pyqs_admin_db import (
    get_levels_by_site_id,
    get_syllabus_by_level,
    get_grades_by_syllabus,
    get_grades_wonderpublish_style,
    get_subjects_by_syllabus,
    get_all_exams,
    get_exam_by_id,
    create_exam,
    update_exam,
    get_exam_documents,
    get_exam_document_by_id,
    create_exam_document,
    update_exam_document_content,
    remove_exam_document,
    get_exam_solutions,
    create_exam_solution,
    update_exam_solution,
    delete_exam_solution,
    delete_all_exam_solutions,
    create_direction,
    get_exam_questions_with_directions
)
from agents.syllabus_processor import SyllabusProcessor
from models.pyqs_admin_models import (
    ExamCreate,
    ExamUpdate,
    ExamDocumentCreate,
    ExamSolutionCreate,
    ExamSolutionUpdate,
    ExtractContentRequest,
    ExtractQuestionsRequest,
    GenerateSolutionsRequest
)
from utils.pyqs_utils import (
    upload_question_paper_to_s3,
    extract_text_from_pdf,
    extract_html_from_pdf,
    extract_questions_from_pdf
)
from utils.pdf_extraction_utils import get_extraction_json_path
import config

logger = logging.getLogger(__name__)

# Initialize the router
router = APIRouter(prefix="/pyqs_admin", tags=["pyqs_admin"])

# Initialize the templates
templates = Jinja2Templates(directory="web/templates")


@router.get("/", response_class=HTMLResponse)
async def pyqs_admin_home(
    request: Request,
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Redirect to the exam list page
    """
    if login_check:
        return login_check

    return RedirectResponse(url="/pyqs_admin/exams")


@router.get("/", response_class=RedirectResponse)
async def pyqs_admin_root(
    request: Request,
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Redirect to the exams list
    """
    if login_check:
        return login_check

    return RedirectResponse(url="/pyqs_admin/exams")


@router.get("/exams", response_class=HTMLResponse)
async def exam_list(
    request: Request,
    page: int = Query(1, ge=1),
    limit: int = Query(10, ge=1, le=100),
    search: str = Query(None),
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Show the list of exams
    """
    if login_check:
        return login_check

    # Calculate offset
    offset = (page - 1) * limit

    # Get exams
    exams, total_count = get_all_exams(limit=limit, offset=offset, search=search)

    # Calculate total pages
    total_pages = math.ceil(total_count / limit)

    return templates.TemplateResponse(
        "pyqs_admin/exam_list.html",
        {
            "request": request,
            "exams": exams,
            "total": total_count,
            "page": page,
            "limit": limit,
            "pages": total_pages,
            "search": search or ""
        }
    )


@router.get("/exams/create", response_class=HTMLResponse)
async def create_exam_form(
    request: Request,
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Show the form for creating a new exam
    """
    if login_check:
        return login_check

    # Get levels for initial dropdown
    levels = get_levels_by_site_id()

    return templates.TemplateResponse(
        "pyqs_admin/create_exam.html",
        {
            "request": request,
            "levels": levels,
            "is_edit": False
        }
    )


@router.post("/exams/create", response_class=HTMLResponse)
async def create_exam_submit(
    request: Request,
    exam_name: str = Form(...),
    level: str = Form(...),
    syllabus: str = Form(...),
    grade: str = Form(...),
    subject: str = Form(...),
    syllabus_text: str = Form(""),
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Handle the form submission for creating a new exam
    """
    if login_check:
        return login_check

    try:
        # Create exam data
        exam_data = {
            "exam_name": exam_name,
            "level": level,
            "syllabus": syllabus,
            "grade": grade,
            "subject": subject,
            "syllabus_text": syllabus_text
        }

        # Get username from session
        username = request.session.get("user", {}).get("username", "system")

        # Create exam
        create_exam(exam_data, username)

        # Redirect to exam list
        return RedirectResponse(url="/pyqs_admin/exams", status_code=303)
    except ValueError as e:
        # Handle duplicate exam error
        logger.warning(f"Duplicate exam error: {e}")

        # Get levels for dropdown
        levels = get_levels_by_site_id()

        # Get syllabi for the selected level
        syllabi = get_syllabus_by_level(level)

        # Get grades for the selected syllabus
        grades = get_grades_by_syllabus(syllabus)

        # Get subjects for the selected syllabus
        subjects = get_subjects_by_syllabus(syllabus)

        return templates.TemplateResponse(
            "pyqs_admin/create_exam.html",
            {
                "request": request,
                "levels": levels,
                "syllabi": syllabi,
                "grades": grades,
                "subjects": subjects,
                "error": str(e),
                "exam_name": exam_name,
                "level": level,
                "syllabus": syllabus,
                "grade": grade,
                "subject": subject,
                "syllabus_text": syllabus_text,
                "is_edit": False
            },
            status_code=400
        )
    except Exception as e:
        logger.error(f"Error creating exam: {e}")

        # Get levels for dropdown
        levels = get_levels_by_site_id()

        return templates.TemplateResponse(
            "pyqs_admin/create_exam.html",
            {
                "request": request,
                "levels": levels,
                "error": f"Error creating exam: {str(e)}",
                "exam_name": exam_name,
                "level": level,
                "syllabus": syllabus,
                "grade": grade,
                "subject": subject,
                "syllabus_text": syllabus_text,
                "is_edit": False
            },
            status_code=400
        )


@router.get("/exams/edit/{exam_id}", response_class=HTMLResponse)
async def edit_exam_form(
    request: Request,
    exam_id: int,
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Show the form for editing an exam
    """
    if login_check:
        return login_check

    # Get exam
    exam = get_exam_by_id(exam_id)
    if not exam:
        raise HTTPException(status_code=404, detail="Exam not found")

    # Get levels for dropdown
    levels = get_levels_by_site_id()

    # Get syllabi for the selected level
    syllabi = get_syllabus_by_level(exam["level"])

    # Get grades for the selected syllabus
    grades = get_grades_by_syllabus(exam["syllabus"])

    # Get subjects for the selected syllabus
    subjects = get_subjects_by_syllabus(exam["syllabus"])

    return templates.TemplateResponse(
        "pyqs_admin/create_exam.html",
        {
            "request": request,
            "exam": exam,
            "levels": levels,
            "syllabi": syllabi,
            "grades": grades,
            "subjects": subjects,
            "is_edit": True
        }
    )


@router.post("/exams/edit/{exam_id}", response_class=HTMLResponse)
async def edit_exam_submit(
    request: Request,
    exam_id: int,
    exam_name: str = Form(...),
    level: str = Form(...),
    syllabus: str = Form(...),
    grade: str = Form(...),
    subject: str = Form(...),
    syllabus_text: str = Form(""),
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Handle the form submission for editing an exam
    """
    if login_check:
        return login_check

    try:
        # Create exam data
        exam_data = {
            "exam_name": exam_name,
            "level": level,
            "syllabus": syllabus,
            "grade": grade,
            "subject": subject,
            "syllabus_text": syllabus_text
        }

        # Update exam
        success = update_exam(exam_id, exam_data)
        if not success:
            raise HTTPException(status_code=404, detail="Exam not found")

        # Redirect to exam list
        return RedirectResponse(url="/pyqs_admin/exams", status_code=303)
    except ValueError as e:
        # Handle duplicate exam error
        logger.warning(f"Duplicate exam error: {e}")

        # Get levels for dropdown
        levels = get_levels_by_site_id()

        # Get syllabi for the selected level
        syllabi = get_syllabus_by_level(level)

        # Get grades for the selected syllabus
        grades = get_grades_by_syllabus(syllabus)

        # Get subjects for the selected syllabus
        subjects = get_subjects_by_syllabus(syllabus)

        return templates.TemplateResponse(
            "pyqs_admin/create_exam.html",
            {
                "request": request,
                "levels": levels,
                "syllabi": syllabi,
                "grades": grades,
                "subjects": subjects,
                "error": str(e),
                "exam": {
                    "id": exam_id,
                    "exam_name": exam_name,
                    "level": level,
                    "syllabus": syllabus,
                    "grade": grade,
                    "subject": subject,
                    "syllabus_text": syllabus_text
                },
                "is_edit": True
            },
            status_code=400
        )
    except Exception as e:
        logger.error(f"Error updating exam: {e}")

        # Get levels for dropdown
        levels = get_levels_by_site_id()

        # Get syllabi for the selected level
        syllabi = get_syllabus_by_level(level)

        # Get grades for the selected syllabus
        grades = get_grades_by_syllabus(syllabus)

        # Get subjects for the selected syllabus
        subjects = get_subjects_by_syllabus(syllabus)

        return templates.TemplateResponse(
            "pyqs_admin/create_exam.html",
            {
                "request": request,
                "levels": levels,
                "syllabi": syllabi,
                "grades": grades,
                "subjects": subjects,
                "error": f"Error updating exam: {str(e)}",
                "exam": {
                    "id": exam_id,
                    "exam_name": exam_name,
                    "level": level,
                    "syllabus": syllabus,
                    "grade": grade,
                    "subject": subject,
                    "syllabus_text": syllabus_text
                },
                "is_edit": True
            },
            status_code=400
        )


# API endpoints for cascading dropdowns
@router.get("/api/levels")
async def get_levels(
    request: Request,
    site_id: int = Query(1),
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Get all levels for a specific site_id
    """
    if login_check:
        return login_check

    levels = get_levels_by_site_id(site_id)
    return {"levels": levels}


@router.get("/api/syllabi/{level}")
async def get_syllabi(
    request: Request,
    level: str,
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Get all syllabi for a specific level in wonderpublish format
    """
    if login_check:
        return login_check

    # Get site_id from session or default to 1 (not used currently)
    site_id = request.session.get("user", {}).get("siteId", 1)

    syllabi = get_syllabus_by_level(level)

    # Return in wonderpublish format
    return {
        "results": syllabi,
        "status": "OK" if syllabi else "Nothing present"
    }


@router.get("/api/grades/{syllabus}")
async def get_grades(
    request: Request,
    syllabus: str,
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Get all grades for a specific syllabus in wonderpublish format
    """
    if login_check:
        return login_check

    # Get site_id from session or default to 1
    site_id = request.session.get("user", {}).get("siteId", 1)

    # Call the wonderpublish style function which returns the complete response
    return get_grades_wonderpublish_style(syllabus, site_id)


@router.get("/api/subjects/{syllabus}")
async def get_subjects(
    request: Request,
    syllabus: str,
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Get all subjects for a specific syllabus in wonderpublish format
    """
    if login_check:
        return login_check

    subjects = get_subjects_by_syllabus(syllabus)

    # Return in wonderpublish format
    return {
        "results": subjects,
        "status": "OK" if subjects else "Nothing present"
    }


# Syllabus processing endpoints
@router.post("/api/exams/{exam_id}/syllabus-to-json")
async def convert_syllabus_to_json(
    request: Request,
    exam_id: int,
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Convert syllabus text to JSON for a specific exam
    """
    if login_check:
        return login_check

    try:
        # Initialize syllabus processor
        processor = SyllabusProcessor()

        # Generate request ID for tracking
        import uuid
        request_id = str(uuid.uuid4())

        # Process syllabus to JSON
        result = processor.process_syllabus_to_json(exam_id, request_id)

        if result["status"] == "success":
            return JSONResponse(
                content={
                    "success": True,
                    "message": result["message"],
                    "request_id": request_id
                }
            )
        else:
            return JSONResponse(
                status_code=400,
                content={
                    "success": False,
                    "error": result["message"],
                    "request_id": request_id
                }
            )

    except Exception as e:
        logger.error(f"Error in syllabus to JSON conversion: {e}")
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "error": f"Internal server error: {str(e)}"
            }
        )


@router.get("/api/exams/{exam_id}/syllabus-json")
async def get_syllabus_json(
    request: Request,
    exam_id: int,
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Get the syllabus JSON for a specific exam
    """
    if login_check:
        return login_check

    try:
        # Initialize syllabus processor
        processor = SyllabusProcessor()

        # Get syllabus JSON
        result = processor.get_syllabus_json(exam_id)

        if result["status"] == "success":
            return JSONResponse(
                content={
                    "success": True,
                    "data": result["data"]
                }
            )
        else:
            return JSONResponse(
                status_code=404,
                content={
                    "success": False,
                    "error": result["message"]
                }
            )

    except Exception as e:
        logger.error(f"Error getting syllabus JSON: {e}")
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "error": f"Internal server error: {str(e)}"
            }
        )


# Document management routes
@router.get("/exams/{exam_id}/documents", response_class=HTMLResponse)
async def exam_documents(
    request: Request,
    exam_id: int,
    page: int = Query(1, ge=1),
    limit: int = Query(10, ge=1, le=100),
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Show the list of documents for a specific exam
    """
    if login_check:
        return login_check

    # Calculate offset
    offset = (page - 1) * limit

    # Get exam details
    exam = get_exam_by_id(exam_id)
    if not exam:
        raise HTTPException(status_code=404, detail="Exam not found")

    # Get documents
    documents, total_count = get_exam_documents(exam_id, limit=limit, offset=offset)

    # Calculate total pages
    total_pages = math.ceil(total_count / limit)

    return templates.TemplateResponse(
        "pyqs_admin/exam_documents.html",
        {
            "request": request,
            "exam": exam,
            "documents": documents,
            "total": total_count,
            "page": page,
            "limit": limit,
            "pages": total_pages
        }
    )


@router.post("/exams/{exam_id}/documents", response_class=HTMLResponse)
async def upload_document(
    request: Request,
    exam_id: int,
    year: int = Form(...),
    month: Optional[str] = Form(None),
    shift: Optional[str] = Form(None),
    file: UploadFile = File(...),
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Upload a new document for a specific exam
    """
    if login_check:
        return login_check

    # Get exam details
    exam = get_exam_by_id(exam_id)
    if not exam:
        raise HTTPException(status_code=404, detail="Exam not found")

    # Check file type
    if not file.filename.lower().endswith(".pdf"):
        return templates.TemplateResponse(
            "pyqs_admin/exam_documents.html",
            {
                "request": request,
                "exam": exam,
                "documents": [],
                "total": 0,
                "page": 1,
                "limit": 10,
                "pages": 0,
                "error": "Only PDF files are allowed"
            }
        )

    try:
        # Read file content
        file_content = await file.read()

        # Upload to S3
        success, s3_path = upload_question_paper_to_s3(
            file_content,
            exam_id,
            year,
            month,
            shift
        )

        if not success:
            return templates.TemplateResponse(
                "pyqs_admin/exam_documents.html",
                {
                    "request": request,
                    "exam": exam,
                    "documents": [],
                    "total": 0,
                    "page": 1,
                    "limit": 10,
                    "pages": 0,
                    "error": "Failed to upload file to S3"
                }
            )

        # Create document record in database
        document_data = {
            "year": year,
            "month": month,
            "shift": shift,
            "question_paper_path": s3_path
        }

        # Get username from session
        username = request.session.get("username", "system")

        # Create document
        document_id = create_exam_document(exam_id, document_data, username)

        # Redirect to documents list
        return RedirectResponse(
            url=f"/pyqs_admin/exams/{exam_id}/documents",
            status_code=303  # HTTP 303 See Other
        )

    except Exception as e:
        logger.error(f"Error uploading document: {e}")
        return templates.TemplateResponse(
            "pyqs_admin/exam_documents.html",
            {
                "request": request,
                "exam": exam,
                "documents": [],
                "total": 0,
                "page": 1,
                "limit": 10,
                "pages": 0,
                "error": f"Error uploading document: {str(e)}"
            }
        )


@router.get("/documents/{document_id}/view", response_class=HTMLResponse)
async def view_document(
    request: Request,
    document_id: int,
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    View a document
    """


@router.get("/documents/{document_id}/pdf")
async def get_pdf(
    request: Request,
    document_id: int,
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Get the PDF file for a document
    """
    if login_check:
        return login_check

    # Get document details
    document = get_exam_document_by_id(document_id)
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")

    # Get the full S3 path
    full_s3_path = os.path.join(config.S3_MOUNT_PATH, document["question_paper_path"])

    # Read the file using the S3 utility function that handles sudo permissions
    file_content = read_file_from_s3(full_s3_path)

    if file_content is None:
        raise HTTPException(status_code=404, detail="PDF file not found or could not be read")

    # Create a temporary file to serve
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".pdf")
    temp_path = temp_file.name

    try:
        # Write the content to the temporary file
        with open(temp_path, "wb") as f:
            f.write(file_content)

        # Return the file with Content-Disposition set to inline to prevent download
        response = FileResponse(
            temp_path,
            media_type="application/pdf",
            filename=os.path.basename(document["question_paper_path"])
        )

        # Set Content-Disposition header to inline to display in browser
        response.headers["Content-Disposition"] = f"inline; filename=\"{os.path.basename(document['question_paper_path'])}\""

        # Set up cleanup callback to delete the temporary file after response is sent
        async def cleanup():
            try:
                os.unlink(temp_path)
            except Exception as e:
                logger.error(f"Error cleaning up temporary file {temp_path}: {e}")

        response.background = cleanup

        return response
    except Exception as e:
        # Clean up the temporary file if an error occurs
        try:
            os.unlink(temp_path)
        except:
            pass

        logger.error(f"Error serving PDF file: {e}")
        raise HTTPException(status_code=500, detail="Error serving PDF file")


@router.post("/documents/{document_id}/extract-content")
async def extract_content(
    request: Request,
    document_id: int,
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Extract HTML content from a document
    """
    if login_check:
        return login_check

    # Get document details
    document = get_exam_document_by_id(document_id)
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")

    try:
        # Extract HTML content
        html_content = extract_html_from_pdf(document["question_paper_path"])
        if not html_content:
            return JSONResponse(
                status_code=500,
                content={"error": "Failed to extract HTML content"}
            )

        # Update document with extracted content
        success = update_exam_document_content(document_id, html_content)
        if not success:
            return JSONResponse(
                status_code=500,
                content={"error": "Failed to update document with extracted content"}
            )

        return JSONResponse(
            content={"success": True, "message": "Content extracted successfully"}
        )

    except Exception as e:
        logger.error(f"Error extracting content: {e}")
        return JSONResponse(
            status_code=500,
            content={"error": f"Error extracting content: {str(e)}"}
        )


@router.get("/documents/{document_id}/view-content", response_class=HTMLResponse)
async def view_content(
    request: Request,
    document_id: int,
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    View the extracted HTML content of a document
    """
    if login_check:
        return login_check

    # Get document details
    document = get_exam_document_by_id(document_id)
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")

    # Check if document has extracted content
    if not document.get("extracted_content"):
        raise HTTPException(status_code=404, detail="Document has no extracted content")

    # Get exam details
    exam = get_exam_by_id(document["exam_id"])
    if not exam:
        raise HTTPException(status_code=404, detail="Exam not found")

    return templates.TemplateResponse(
        "pyqs_admin/view_content.html",
        {
            "request": request,
            "document": document,
            "exam": exam
        }
    )


@router.post("/documents/extract-questions")
async def extract_questions(
    request: Request,
    request_data: dict = Body(...),
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Extract questions from a document
    """
    if login_check:
        return login_check

    try:
        # Validate request data
        if not request_data or "total_questions" not in request_data:
            raise HTTPException(status_code=422, detail="total_questions is required")

        total_questions = request_data.get("total_questions")
        document_id = request_data.get("document_id")

        # Validate total_questions
        if not isinstance(total_questions, int) or total_questions < 1 or total_questions > 500:
            raise HTTPException(status_code=422, detail="total_questions must be an integer between 1 and 500")

        # Get document details
        document = get_exam_document_by_id(document_id)
        if not document:
            raise HTTPException(status_code=404, detail="Document not found")

        # Get the question paper path
        question_paper_path = document.get("question_paper_path")
        if not question_paper_path:
            raise HTTPException(status_code=400, detail="Document has no question paper path")

        # Import the service
        from services.document_question_extractor_service import DocumentQuestionExtractorService

        # Create service instance
        extractor_service = DocumentQuestionExtractorService()

        # Get username from session
        username = request.session.get("username", "unknown")

        # Start the extraction process (this will run in background)
        result = await extractor_service.extract_questions_from_document(
            document_id=document_id,
            question_paper_path=question_paper_path,
            total_questions=total_questions,
            username=username
        )

        if result["status"] == "error":
            return JSONResponse(
                status_code=500,
                content={"error": result["message"]}
            )

        # Return task ID for polling
        return JSONResponse(
            content={
                "success": True,
                "message": "Question extraction started",
                "task_id": result["task_id"]
            }
        )

    except Exception as e:
        logger.error(f"Error starting question extraction: {e}")
        return JSONResponse(
            status_code=500,
            content={"error": f"Error starting question extraction: {str(e)}"}
        )


@router.post("/documents/{document_id}/generate-solutions")
async def generate_solutions(
    request: Request,
    document_id: int,
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Generate solutions for all questions in a document
    """
    if login_check:
        return login_check

    try:
        # Get document details to verify it exists
        document = get_exam_document_by_id(document_id)
        if not document:
            raise HTTPException(status_code=404, detail="Document not found")

        # Check if document has questions
        questions = get_exam_questions_with_directions(document_id)
        if not questions:
            return JSONResponse(
                status_code=400,
                content={"error": "No questions found for this document. Please extract questions first."}
            )

        # Import the service
        from services.document_solution_generator_service import DocumentSolutionGeneratorService

        # Create service instance
        generator_service = DocumentSolutionGeneratorService()

        # Get username from session
        username = request.session.get("username", "unknown")

        # Start the solution generation process (this will run in background)
        result = await generator_service.generate_solutions_for_document(
            document_id=document_id,
            username=username
        )

        if result["status"] == "success":
            return JSONResponse(
                content={
                    "success": True,
                    "task_id": result["task_id"],
                    "message": "Solution generation started successfully"
                }
            )
        else:
            return JSONResponse(
                status_code=500,
                content={"error": result["message"]}
            )

    except Exception as e:
        logger.error(f"Error starting solution generation: {e}")
        return JSONResponse(
            status_code=500,
            content={"error": f"Error starting solution generation: {str(e)}"}
        )


@router.get("/documents/extraction-status/{task_id}")
async def get_extraction_status(
    request: Request,
    task_id: str,
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Get the status of a question extraction task
    """
    if login_check:
        return login_check

    try:
        # Import the service
        from services.request_tracker_service import RequestTrackerService

        # Create service instance
        tracker_service = RequestTrackerService()

        # Get task status
        status_info = tracker_service.get_task_status(task_id)

        return JSONResponse(content=status_info)

    except Exception as e:
        logger.error(f"Error getting extraction status: {e}")
        return JSONResponse(
            status_code=500,
            content={"error": f"Error getting extraction status: {str(e)}"}
        )


@router.get("/documents/{document_id}/edit-questions", response_class=HTMLResponse)
async def edit_questions(
    request: Request,
    document_id: int,
    page: int = Query(1, ge=1),
    limit: int = Query(20, ge=1, le=100),
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Edit the extracted questions of a document
    """
    if login_check:
        return login_check

    # Get document details to get the exam_id
    document = get_exam_document_by_id(document_id)
    exam_id = document.get("content_exam_mst_id") if document else None

    return templates.TemplateResponse(
        "pyqs_admin/edit_questions.html",
        {
            "request": request,
            "document_id": document_id,
            "exam_id": exam_id,
            "page": page,
            "limit": limit
        }
    )


@router.get("/api/documents/{document_id}")
async def get_document_details(
    request: Request,
    document_id: int,
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Get document details including exam_id
    """
    if login_check:
        return login_check

    try:
        # Get document details
        document = get_exam_document_by_id(document_id)

        if not document:
            return JSONResponse(
                status_code=404,
                content={
                    "success": False,
                    "error": "Document not found"
                }
            )

        return JSONResponse(
            content={
                "success": True,
                "document": document
            }
        )

    except Exception as e:
        logger.error(f"Error getting document details: {e}")
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "error": f"Error getting document details: {str(e)}"
            }
        )


@router.get("/api/documents/{document_id}/questions")
async def get_document_questions(
    request: Request,
    document_id: int,
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Get all questions for a specific document with directions included
    """
    if login_check:
        return login_check

    try:
        # Get questions with directions
        questions = get_exam_questions_with_directions(document_id)

        return JSONResponse(
            content={
                "success": True,
                "questions": questions,
                "total": len(questions)
            }
        )

    except Exception as e:
        logger.error(f"Error getting document questions: {e}")
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "error": f"Error getting document questions: {str(e)}"
            }
        )

@router.post("/upload-image")
async def upload_image_for_editor(
    request: Request,
    upload: UploadFile = File(...),
    document_id: Optional[int] = Form(None),
    question_number: Optional[int] = Form(None),
    editor_type: Optional[str] = Form(None),
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Upload an image for CKEditor (supports both CKEditor 4 and 5 formats)
    """
    if login_check:
        return login_check

    try:
        # Handle CKEditor 4 query parameters
        query_params = request.query_params
        ckeditor_name = query_params.get('CKEditor')
        ckeditor_func_num = query_params.get('CKEditorFuncNum')

        # Get parameters from form data or query parameters
        if document_id is None:
            document_id = query_params.get('document_id')
            if document_id:
                document_id = int(document_id)

        if question_number is None:
            question_number = query_params.get('question_number')
            if question_number:
                question_number = int(question_number)

        if editor_type is None:
            editor_type = query_params.get('editor_type', 'question')

        # Validate required parameters
        if not document_id or document_id <= 0:
            error_msg = "Invalid document_id parameter"
            if ckeditor_func_num:
                # CKEditor 4 format response
                return HTMLResponse(
                    content=f"<script>window.parent.CKEDITOR.tools.callFunction({ckeditor_func_num}, '', '{error_msg}');</script>",
                    status_code=400
                )
            else:
                # JSON response for CKEditor 5 or AJAX
                return JSONResponse(
                    status_code=400,
                    content={"error": {"message": error_msg}}
                )

        if not question_number or question_number <= 0:
            error_msg = "Invalid question_number parameter"
            if ckeditor_func_num:
                return HTMLResponse(
                    content=f"<script>window.parent.CKEDITOR.tools.callFunction({ckeditor_func_num}, '', '{error_msg}');</script>",
                    status_code=400
                )
            else:
                return JSONResponse(
                    status_code=400,
                    content={"error": {"message": error_msg}}
                )

        if not editor_type or editor_type not in ['question', 'option1', 'option2', 'option3', 'option4', 'option5', 'solution', 'direction']:
            error_msg = "Invalid editor_type parameter"
            if ckeditor_func_num:
                return HTMLResponse(
                    content=f"<script>window.parent.CKEDITOR.tools.callFunction({ckeditor_func_num}, '', '{error_msg}');</script>",
                    status_code=400
                )
            else:
                return JSONResponse(
                    status_code=400,
                    content={"error": {"message": error_msg}}
                )

        logger.info(f"Uploading image for document_id: {document_id}, question_number: {question_number}, editor_type: {editor_type}, ckeditor_func_num: {ckeditor_func_num}")

        # Validate file type
        allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
        if upload.content_type not in allowed_types:
            error_msg = "Invalid file type. Only JPEG, PNG, GIF, and WebP images are allowed."
            if ckeditor_func_num:
                return HTMLResponse(
                    content=f"<script>window.parent.CKEDITOR.tools.callFunction({ckeditor_func_num}, '', '{error_msg}');</script>",
                    status_code=400
                )
            else:
                return JSONResponse(
                    status_code=400,
                    content={"error": {"message": error_msg}}
                )

        # Validate file size (max 5MB)
        max_size = 5 * 1024 * 1024  # 5MB
        file_content = await upload.read()
        if len(file_content) > max_size:
            error_msg = "File size too large. Maximum size is 5MB."
            if ckeditor_func_num:
                return HTMLResponse(
                    content=f"<script>window.parent.CKEDITOR.tools.callFunction({ckeditor_func_num}, '', '{error_msg}');</script>",
                    status_code=400
                )
            else:
                return JSONResponse(
                    status_code=400,
                    content={"error": {"message": error_msg}}
                )

        # Create a temporary file
        import tempfile

        # Generate filename based on editor type
        # Always use .png extension for consistency
        if editor_type.startswith('option'):
            # Extract option number (option1 -> 1, option2 -> 2, etc.)
            option_number = editor_type[-1]
            question_filename = f"question_{question_number}_option_{option_number}.png"
        elif editor_type == 'question':
            question_filename = f"question_{question_number}.png"
        elif editor_type == 'solution':
            question_filename = f"question_{question_number}_solution.png"
        elif editor_type == 'direction':
            question_filename = f"question_{question_number}_direction.png"
        else:
            # Fallback
            question_filename = f"question_{question_number}_{editor_type}.png"

        with tempfile.NamedTemporaryFile(delete=False, suffix=".png") as temp_file:
            temp_file.write(file_content)
            temp_file_path = temp_file.name

        try:
            # Upload to S3 using existing utility
            from utils.s3_utils import upload_file_to_s3

            # Use the correct path structure: supload/pdfextracts/documents/{document_id}/main/extractedQuizImages/question_{question_id}.png
            s3_path = upload_file_to_s3(
                temp_file_path,
                book_id="documents",
                chapter_id=str(document_id),
                res_id="main",
                file_name=question_filename,
                is_quiz_image=True
            )

            if s3_path:
                # Build CDN path based on config.CURRENT_ENV
                import config
                current_env = getattr(config, 'CURRENT_ENV', 'qa').lower()
                if current_env == 'publish':
                    current_env = 'live'
                cdn_path = f"https://d1xcofdbxwssh7.cloudfront.net/{current_env}/"
                image_url = f"{cdn_path}{s3_path}"

                # Return appropriate response format
                if ckeditor_func_num:
                    # CKEditor 4 format response
                    return HTMLResponse(
                        content=f"<script>window.parent.CKEDITOR.tools.callFunction({ckeditor_func_num}, '{image_url}', 'Image uploaded successfully');</script>"
                    )
                else:
                    # JSON response for CKEditor 5 or AJAX
                    return JSONResponse(
                        content={
                            "url": image_url
                        }
                    )
            else:
                error_msg = "Failed to upload image to S3"
                if ckeditor_func_num:
                    return HTMLResponse(
                        content=f"<script>window.parent.CKEDITOR.tools.callFunction({ckeditor_func_num}, '', '{error_msg}');</script>",
                        status_code=500
                    )
                else:
                    return JSONResponse(
                        status_code=500,
                        content={"error": {"message": error_msg}}
                    )

        finally:
            # Clean up temporary file
            import os
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)

    except Exception as e:
        logger.error(f"Error uploading image: {e}")
        error_msg = f"Error uploading image: {str(e)}"

        # Try to get ckeditor_func_num from query params for error response
        try:
            query_params = request.query_params
            ckeditor_func_num = query_params.get('CKEditorFuncNum')
            if ckeditor_func_num:
                return HTMLResponse(
                    content=f"<script>window.parent.CKEDITOR.tools.callFunction({ckeditor_func_num}, '', '{error_msg}');</script>",
                    status_code=500
                )
        except:
            pass

        return JSONResponse(
            status_code=500,
            content={"error": {"message": error_msg}}
        )


@router.post("/solutions/{question_id}")
async def update_question_solution(
    request: Request,
    question_id: int,
    solution_data: dict = Body(...),
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Update/save an edited question solution
    """
    if login_check:
        return login_check

    try:
        # Prepare solution data for update (including directions)
        update_data = {
            "question": solution_data.get("question", ""),
            "question_type": solution_data.get("question_type", ""),
            "option1": solution_data.get("option1", ""),
            "option2": solution_data.get("option2", ""),
            "option3": solution_data.get("option3", ""),
            "option4": solution_data.get("option4", ""),
            "option5": solution_data.get("option5", ""),
            "answer": solution_data.get("answer", ""),
            "marks": float(solution_data.get("marks")) if solution_data.get("marks") and str(solution_data.get("marks")).strip() else None,
            "negative_mark": float(solution_data.get("negative_mark")) if solution_data.get("negative_mark") and str(solution_data.get("negative_mark")).strip() else None,
            "topic": solution_data.get("topic", ""),
            "subtopic": solution_data.get("subtopic", ""),
            "solution": solution_data.get("solution", ""),
            "directions": solution_data.get("directions", "")  # Include directions in the data
        }

        # Update the solution in database
        success = update_exam_solution(question_id, update_data)

        if success:
            return JSONResponse(
                content={"success": True, "message": "Question updated successfully"}
            )
        else:
            return JSONResponse(
                status_code=500,
                content={"success": False, "error": "Failed to update question"}
            )

    except Exception as e:
        logger.error(f"Error updating question solution {question_id}: {e}")
        return JSONResponse(
            status_code=500,
            content={"success": False, "error": f"Error updating question: {str(e)}"}
        )


@router.post("/documents/{document_id}/delete")
async def delete_document(
    request: Request,
    document_id: int,
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Delete a document and all related files and data
    """
    if login_check:
        return login_check

    try:
        # Get document details
        document = get_exam_document_by_id(document_id)
        if not document:
            return JSONResponse(
                status_code=404,
                content={"error": "Document not found"}
            )

        # Get the PDF path
        pdf_path = document.get("question_paper_path")
        if pdf_path:
            # Get the full S3 path
            full_pdf_path = os.path.join(config.S3_MOUNT_PATH, pdf_path)

            # Get the JSON path for extracted content
            json_path = get_extraction_json_path(pdf_path)
            full_json_path = os.path.join(config.S3_MOUNT_PATH, json_path)

            # Delete the files if they exist
            try:
                if os.path.exists(full_pdf_path):
                    os.remove(full_pdf_path)
                    logger.info(f"Deleted PDF file: {full_pdf_path}")

                if os.path.exists(full_json_path):
                    os.remove(full_json_path)
                    logger.info(f"Deleted JSON file: {full_json_path}")

                # Delete any image files that might have been created
                pdf_dir = os.path.dirname(full_pdf_path)
                pdf_name = os.path.basename(full_pdf_path).split('.')[0]
                image_dir = os.path.join(pdf_dir, f"{pdf_name}_images")
                if os.path.exists(image_dir) and os.path.isdir(image_dir):
                    shutil.rmtree(image_dir)
                    logger.info(f"Deleted image directory: {image_dir}")
            except Exception as e:
                logger.error(f"Error deleting files: {e}")
                # Continue with database deletion even if file deletion fails

        # Delete all solutions for this document
        delete_all_exam_solutions(document_id)

        # Delete the document from the database
        success = remove_exam_document(document_id)
        if not success:
            return JSONResponse(
                status_code=500,
                content={"error": "Failed to delete document from database"}
            )

        return JSONResponse(
            content={"success": True, "message": "Document deleted successfully"}
        )

    except Exception as e:
        logger.error(f"Error deleting document: {e}")
        return JSONResponse(
            status_code=500,
            content={"error": f"Error deleting document: {str(e)}"}
        )
